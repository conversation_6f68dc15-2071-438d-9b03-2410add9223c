# FreeRADIUS Google SSO - One-Click Installer Usage Guide

## 🎯 Overview

The one-click installer provides a complete, automated deployment solution for FreeRADIUS with Google SSO authentication on Windows Server. It eliminates the need for manual configuration and reduces deployment time from hours to minutes.

## 📦 For Distributors/Developers

### Creating the Installer Package

1. **Prepare the source code** (ensure all files are present)
2. **Run the package creator** on a Windows machine:
   ```powershell
   .\create-deployment-package.ps1
   ```
3. **Distribute the ZIP file** to target Windows servers

### Package Contents

The generated ZIP file contains:
- **installer-launcher.bat** - Main entry point (run as Administrator)
- **FreeRADIUS-GoogleSSO-Installer.ps1** - GUI installer with automation
- **freeradius-manager.ps1** - Post-installation management tool
- **Source code and configuration files**
- **Documentation and examples**

## 👨‍💼 For IT Administrators

### Prerequisites

Before installation, ensure you have:

1. **Windows Server 2019/2022** or Windows 10/11 Pro
2. **Administrator privileges** on the target machine
3. **Internet connection** for downloading dependencies
4. **Google Workspace admin access**
5. **Google service account JSON key file**

### Google Workspace Setup

1. **Create Service Account**:
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create a new project or select existing
   - Enable Admin SDK API
   - Create service account with domain-wide delegation
   - Download JSON key file

2. **Configure OAuth Scopes**:
   - Add required scopes for user authentication
   - Enable directory access for the service account

### Installation Steps

#### Step 1: Download and Extract
1. Download the installer ZIP file
2. Extract to a folder (e.g., `C:\temp\freeradius-installer`)
3. Ensure all files are present

#### Step 2: Run the Installer
1. **Right-click** `installer-launcher.bat`
2. Select **"Run as administrator"**
3. Follow the prompts in the console window

#### Step 3: Configure via GUI
The installer will launch a GUI where you'll enter:

- **Google Workspace Domain**: Your organization's domain (e.g., company.com)
- **Admin Email**: Your Google Workspace admin email
- **Service Account Key**: Path to the downloaded JSON file
- **RADIUS Secret**: Shared secret for WiFi access points (default: testing123)
- **Installation Options**: WSL2 deployment (recommended)

#### Step 4: Wait for Completion
The installer will automatically:
- Install WSL2 and Ubuntu (may require restart)
- Download and compile FreeRADIUS
- Build and install the Google SSO module
- Configure Windows service
- Set up firewall rules
- Create management shortcuts

### What Happens During Installation

```
[1/6] Installing Prerequisites
├── Chocolatey package manager
├── WSL2 and Ubuntu distribution
├── Git, 7-Zip, NSSM tools
└── System restart (if needed)

[2/6] Setting Up WSL2 Environment
├── Ubuntu user configuration
├── Package updates
└── FreeRADIUS dependencies

[3/6] Building and Installing Module
├── Source code compilation
├── Module installation
├── Configuration setup
└── Service account integration

[4/6] Windows Service Configuration
├── Service wrapper creation
├── Event log integration
├── Automatic startup setup
└── Monitoring configuration

[5/6] Network and Security Setup
├── Windows Firewall rules
├── Port configuration (1812, 1813)
└── Security permissions

[6/6] Final Configuration
├── Desktop shortcuts
├── Management tools
├── Testing and validation
└── Completion summary
```

## 🛠️ Post-Installation Management

### Using the FreeRADIUS Manager

Launch from desktop shortcut or run:
```powershell
C:\FreeRADIUS-GoogleSSO\freeradius-manager.ps1
```

**Features**:
- **Service Status**: Real-time monitoring of Windows service and FreeRADIUS
- **Service Control**: Start, stop, restart operations
- **Authentication Testing**: Built-in test tool for user authentication
- **Quick Actions**: Access to logs, configuration, and debug mode

### Command Line Management

```powershell
# Check service status
Get-Service FreeRADIUS-GoogleSSO

# View recent logs
Get-EventLog -LogName Application -Source FreeRADIUS-GoogleSSO -Newest 10

# Test authentication
wsl -d Ubuntu-20.04 radtest <EMAIL> password localhost 0 testing123

# Debug mode
wsl -d Ubuntu-20.04 sudo freeradius -X
```

## 🌐 WiFi Infrastructure Configuration

### Access Point Setup

Configure your WiFi access points with:
- **RADIUS Server IP**: Your Windows server IP address
- **Authentication Port**: 1812
- **Accounting Port**: 1813
- **Shared Secret**: The secret you configured during installation

### Client Device Configuration

Users should configure their devices with:
- **Security**: WPA2-Enterprise
- **EAP Method**: TTLS
- **Phase 2 Authentication**: PAP
- **Username**: <EMAIL>
- **Password**: Their Google Workspace password

## 🔧 Troubleshooting

### Common Issues

1. **Installation Fails at WSL2 Setup**:
   - Ensure Windows version supports WSL2
   - Check if virtualization is enabled in BIOS
   - Restart may be required

2. **Service Won't Start**:
   - Check Event Viewer for detailed errors
   - Verify Google service account key is valid
   - Ensure network connectivity to Google APIs

3. **Authentication Fails**:
   - Verify user exists in Google Workspace
   - Check service account permissions
   - Review FreeRADIUS debug output

### Debug Steps

1. **Check Installation Log**:
   ```
   C:\FreeRADIUS-GoogleSSO\install.log
   ```

2. **View Service Logs**:
   ```
   C:\FreeRADIUS-GoogleSSO\service-output.log
   C:\FreeRADIUS-GoogleSSO\service-error.log
   ```

3. **Test FreeRADIUS Configuration**:
   ```bash
   wsl -d Ubuntu-20.04 sudo freeradius -C
   ```

4. **Run in Debug Mode**:
   ```bash
   wsl -d Ubuntu-20.04 sudo freeradius -X
   ```

## 📊 Monitoring and Maintenance

### Health Checks

The installer sets up monitoring for:
- **Windows Service Status**: Automatic restart on failure
- **FreeRADIUS Process**: WSL2 service monitoring
- **Event Logging**: Integration with Windows Event Log
- **Performance Metrics**: Basic service health indicators

### Regular Maintenance

1. **Weekly**: Check service status and logs
2. **Monthly**: Review authentication statistics
3. **Quarterly**: Update Google service account keys if needed
4. **Annually**: Review and update configuration

## 🎉 Success Indicators

After successful installation, you should see:

✅ **Windows Service**: "FreeRADIUS-GoogleSSO" running in Services console
✅ **Desktop Shortcuts**: Management tools available on desktop
✅ **Network Access**: Firewall rules configured for RADIUS ports
✅ **Authentication**: Test users can authenticate successfully
✅ **Monitoring**: Event logs showing service activity

## 📞 Support and Resources

### Documentation
- `ONE_CLICK_INSTALLER_README.md` - Complete installer guide
- `WINDOWS_DEPLOYMENT_GUIDE.md` - Detailed deployment information
- `docs/google_workspace_setup.md` - Google Workspace configuration

### Log Locations
- **Installation**: `C:\FreeRADIUS-GoogleSSO\install.log`
- **Service**: `C:\FreeRADIUS-GoogleSSO\service-*.log`
- **FreeRADIUS**: `/var/log/freeradius/` (in WSL2)
- **Windows Events**: Application log, source "FreeRADIUS-GoogleSSO"

### Getting Help

1. **Check logs first** - Most issues are logged with details
2. **Use debug mode** - Run `freeradius -X` for detailed output
3. **Verify Google setup** - Ensure service account and APIs are configured
4. **Test manually** - Use `radtest` to isolate authentication issues

The one-click installer significantly simplifies FreeRADIUS deployment while maintaining enterprise-grade security and functionality!
