# freeradius-manager.ps1
# FreeRADIUS Google SSO Management Utility
# Provides a GUI for managing the FreeRADIUS service and testing authentication

param(
    [switch]$Help
)

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

$script:ServiceName = "FreeRADIUS-GoogleSSO"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-ServiceStatus {
    try {
        $service = Get-Service -Name $script:ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            return @{
                Exists = $true
                Status = $service.Status
                StartType = $service.StartType
                DisplayName = $service.DisplayName
            }
        } else {
            return @{
                Exists = $false
                Status = "Not Installed"
                StartType = "N/A"
                DisplayName = "N/A"
            }
        }
    } catch {
        return @{
            Exists = $false
            Status = "Error"
            StartType = "N/A"
            DisplayName = "N/A"
        }
    }
}

function Get-FreeRADIUSStatus {
    try {
        $status = wsl -d Ubuntu-20.04 systemctl is-active freeradius 2>$null
        return $status.Trim()
    } catch {
        return "Unknown"
    }
}

function Start-FreeRADIUSService {
    try {
        Start-Service -Name $script:ServiceName
        return $true
    } catch {
        Write-Log "Error starting service: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Stop-FreeRADIUSService {
    try {
        Stop-Service -Name $script:ServiceName -Force
        return $true
    } catch {
        Write-Log "Error stopping service: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Restart-FreeRADIUSService {
    try {
        Restart-Service -Name $script:ServiceName
        return $true
    } catch {
        Write-Log "Error restarting service: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-Authentication {
    param([string]$Username, [string]$Password, [string]$Secret = "testing123")
    
    try {
        $result = wsl -d Ubuntu-20.04 radtest $Username $Password localhost 0 $Secret 2>&1
        return @{
            Success = $LASTEXITCODE -eq 0
            Output = $result -join "`n"
        }
    } catch {
        return @{
            Success = $false
            Output = "Error running test: $($_.Exception.Message)"
        }
    }
}

function Show-MainWindow {
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "FreeRADIUS Google SSO Manager"
    $form.Size = New-Object System.Drawing.Size(700, 600)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    
    # Service Status Group
    $statusGroup = New-Object System.Windows.Forms.GroupBox
    $statusGroup.Location = New-Object System.Drawing.Point(20, 20)
    $statusGroup.Size = New-Object System.Drawing.Size(650, 120)
    $statusGroup.Text = "Service Status"
    $form.Controls.Add($statusGroup)
    
    # Windows Service Status
    $winServiceLabel = New-Object System.Windows.Forms.Label
    $winServiceLabel.Location = New-Object System.Drawing.Point(20, 30)
    $winServiceLabel.Size = New-Object System.Drawing.Size(150, 20)
    $winServiceLabel.Text = "Windows Service:"
    $statusGroup.Controls.Add($winServiceLabel)
    
    $winServiceStatus = New-Object System.Windows.Forms.Label
    $winServiceStatus.Location = New-Object System.Drawing.Point(180, 30)
    $winServiceStatus.Size = New-Object System.Drawing.Size(200, 20)
    $winServiceStatus.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9, [System.Drawing.FontStyle]::Bold)
    $statusGroup.Controls.Add($winServiceStatus)
    
    # FreeRADIUS Status
    $radiusLabel = New-Object System.Windows.Forms.Label
    $radiusLabel.Location = New-Object System.Drawing.Point(20, 55)
    $radiusLabel.Size = New-Object System.Drawing.Size(150, 20)
    $radiusLabel.Text = "FreeRADIUS (WSL2):"
    $statusGroup.Controls.Add($radiusLabel)
    
    $radiusStatus = New-Object System.Windows.Forms.Label
    $radiusStatus.Location = New-Object System.Drawing.Point(180, 55)
    $radiusStatus.Size = New-Object System.Drawing.Size(200, 20)
    $radiusStatus.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9, [System.Drawing.FontStyle]::Bold)
    $statusGroup.Controls.Add($radiusStatus)
    
    # Refresh Button
    $refreshButton = New-Object System.Windows.Forms.Button
    $refreshButton.Location = New-Object System.Drawing.Point(550, 30)
    $refreshButton.Size = New-Object System.Drawing.Size(80, 30)
    $refreshButton.Text = "Refresh"
    $refreshButton.Add_Click({
        Update-StatusDisplay
    })
    $statusGroup.Controls.Add($refreshButton)
    
    # Service Control Group
    $controlGroup = New-Object System.Windows.Forms.GroupBox
    $controlGroup.Location = New-Object System.Drawing.Point(20, 150)
    $controlGroup.Size = New-Object System.Drawing.Size(650, 80)
    $controlGroup.Text = "Service Control"
    $form.Controls.Add($controlGroup)
    
    $startButton = New-Object System.Windows.Forms.Button
    $startButton.Location = New-Object System.Drawing.Point(20, 30)
    $startButton.Size = New-Object System.Drawing.Size(80, 30)
    $startButton.Text = "Start"
    $startButton.Add_Click({
        if (Start-FreeRADIUSService) {
            [System.Windows.Forms.MessageBox]::Show("Service started successfully", "Success", "OK", "Information")
            Update-StatusDisplay
        } else {
            [System.Windows.Forms.MessageBox]::Show("Failed to start service", "Error", "OK", "Error")
        }
    })
    $controlGroup.Controls.Add($startButton)
    
    $stopButton = New-Object System.Windows.Forms.Button
    $stopButton.Location = New-Object System.Drawing.Point(120, 30)
    $stopButton.Size = New-Object System.Drawing.Size(80, 30)
    $stopButton.Text = "Stop"
    $stopButton.Add_Click({
        if (Stop-FreeRADIUSService) {
            [System.Windows.Forms.MessageBox]::Show("Service stopped successfully", "Success", "OK", "Information")
            Update-StatusDisplay
        } else {
            [System.Windows.Forms.MessageBox]::Show("Failed to stop service", "Error", "OK", "Error")
        }
    })
    $controlGroup.Controls.Add($stopButton)
    
    $restartButton = New-Object System.Windows.Forms.Button
    $restartButton.Location = New-Object System.Drawing.Point(220, 30)
    $restartButton.Size = New-Object System.Drawing.Size(80, 30)
    $restartButton.Text = "Restart"
    $restartButton.Add_Click({
        if (Restart-FreeRADIUSService) {
            [System.Windows.Forms.MessageBox]::Show("Service restarted successfully", "Success", "OK", "Information")
            Update-StatusDisplay
        } else {
            [System.Windows.Forms.MessageBox]::Show("Failed to restart service", "Error", "OK", "Error")
        }
    })
    $controlGroup.Controls.Add($restartButton)
    
    # Authentication Test Group
    $testGroup = New-Object System.Windows.Forms.GroupBox
    $testGroup.Location = New-Object System.Drawing.Point(20, 240)
    $testGroup.Size = New-Object System.Drawing.Size(650, 150)
    $testGroup.Text = "Authentication Test"
    $form.Controls.Add($testGroup)
    
    # Username
    $usernameLabel = New-Object System.Windows.Forms.Label
    $usernameLabel.Location = New-Object System.Drawing.Point(20, 30)
    $usernameLabel.Size = New-Object System.Drawing.Size(80, 20)
    $usernameLabel.Text = "Username:"
    $testGroup.Controls.Add($usernameLabel)
    
    $usernameTextBox = New-Object System.Windows.Forms.TextBox
    $usernameTextBox.Location = New-Object System.Drawing.Point(110, 28)
    $usernameTextBox.Size = New-Object System.Drawing.Size(200, 20)
    $usernameTextBox.Text = "<EMAIL>"
    $testGroup.Controls.Add($usernameTextBox)
    
    # Password
    $passwordLabel = New-Object System.Windows.Forms.Label
    $passwordLabel.Location = New-Object System.Drawing.Point(330, 30)
    $passwordLabel.Size = New-Object System.Drawing.Size(80, 20)
    $passwordLabel.Text = "Password:"
    $testGroup.Controls.Add($passwordLabel)
    
    $passwordTextBox = New-Object System.Windows.Forms.TextBox
    $passwordTextBox.Location = New-Object System.Drawing.Point(420, 28)
    $passwordTextBox.Size = New-Object System.Drawing.Size(150, 20)
    $passwordTextBox.UseSystemPasswordChar = $true
    $testGroup.Controls.Add($passwordTextBox)
    
    # Test Button
    $testButton = New-Object System.Windows.Forms.Button
    $testButton.Location = New-Object System.Drawing.Point(580, 26)
    $testButton.Size = New-Object System.Drawing.Size(60, 25)
    $testButton.Text = "Test"
    $testButton.Add_Click({
        if ([string]::IsNullOrWhiteSpace($usernameTextBox.Text) -or [string]::IsNullOrWhiteSpace($passwordTextBox.Text)) {
            [System.Windows.Forms.MessageBox]::Show("Please enter both username and password", "Validation Error", "OK", "Warning")
            return
        }
        
        $testResult = Test-Authentication -Username $usernameTextBox.Text -Password $passwordTextBox.Text
        $testOutput.Text = $testResult.Output
        
        if ($testResult.Success) {
            $testOutput.ForeColor = [System.Drawing.Color]::Green
        } else {
            $testOutput.ForeColor = [System.Drawing.Color]::Red
        }
    })
    $testGroup.Controls.Add($testButton)
    
    # Test Output
    $testOutput = New-Object System.Windows.Forms.TextBox
    $testOutput.Location = New-Object System.Drawing.Point(20, 60)
    $testOutput.Size = New-Object System.Drawing.Size(620, 80)
    $testOutput.Multiline = $true
    $testOutput.ScrollBars = "Vertical"
    $testOutput.ReadOnly = $true
    $testOutput.Font = New-Object System.Drawing.Font("Consolas", 8)
    $testGroup.Controls.Add($testOutput)
    
    # Logs Group
    $logsGroup = New-Object System.Windows.Forms.GroupBox
    $logsGroup.Location = New-Object System.Drawing.Point(20, 400)
    $logsGroup.Size = New-Object System.Drawing.Size(650, 120)
    $logsGroup.Text = "Quick Actions"
    $form.Controls.Add($logsGroup)
    
    $viewLogsButton = New-Object System.Windows.Forms.Button
    $viewLogsButton.Location = New-Object System.Drawing.Point(20, 30)
    $viewLogsButton.Size = New-Object System.Drawing.Size(100, 30)
    $viewLogsButton.Text = "View Event Logs"
    $viewLogsButton.Add_Click({
        Start-Process "eventvwr.msc" -ArgumentList "/c:Application /f:*FreeRADIUS*"
    })
    $logsGroup.Controls.Add($viewLogsButton)
    
    $servicesButton = New-Object System.Windows.Forms.Button
    $servicesButton.Location = New-Object System.Drawing.Point(140, 30)
    $servicesButton.Size = New-Object System.Drawing.Size(100, 30)
    $servicesButton.Text = "Services Console"
    $servicesButton.Add_Click({
        Start-Process "services.msc"
    })
    $logsGroup.Controls.Add($servicesButton)
    
    $debugButton = New-Object System.Windows.Forms.Button
    $debugButton.Location = New-Object System.Drawing.Point(260, 30)
    $debugButton.Size = New-Object System.Drawing.Size(100, 30)
    $debugButton.Text = "Debug Mode"
    $debugButton.Add_Click({
        Start-Process "wt" -ArgumentList "wsl -d Ubuntu-20.04 sudo freeradius -X"
    })
    $logsGroup.Controls.Add($debugButton)
    
    $configButton = New-Object System.Windows.Forms.Button
    $configButton.Location = New-Object System.Drawing.Point(380, 30)
    $configButton.Size = New-Object System.Drawing.Size(100, 30)
    $configButton.Text = "Edit Config"
    $configButton.Add_Click({
        Start-Process "notepad" -ArgumentList "C:\FreeRADIUS-GoogleSSO\config-backup.txt"
    })
    $logsGroup.Controls.Add($configButton)
    
    # Update status display function
    $script:UpdateStatusDisplay = {
        $serviceStatus = Get-ServiceStatus
        $radiusStatusText = Get-FreeRADIUSStatus
        
        if ($serviceStatus.Exists) {
            $winServiceStatus.Text = $serviceStatus.Status
            switch ($serviceStatus.Status) {
                "Running" { $winServiceStatus.ForeColor = [System.Drawing.Color]::Green }
                "Stopped" { $winServiceStatus.ForeColor = [System.Drawing.Color]::Red }
                default { $winServiceStatus.ForeColor = [System.Drawing.Color]::Orange }
            }
        } else {
            $winServiceStatus.Text = "Not Installed"
            $winServiceStatus.ForeColor = [System.Drawing.Color]::Red
        }
        
        $radiusStatus.Text = $radiusStatusText
        switch ($radiusStatusText) {
            "active" { $radiusStatus.ForeColor = [System.Drawing.Color]::Green }
            "inactive" { $radiusStatus.ForeColor = [System.Drawing.Color]::Red }
            "failed" { $radiusStatus.ForeColor = [System.Drawing.Color]::Red }
            default { $radiusStatus.ForeColor = [System.Drawing.Color]::Orange }
        }
    }
    
    # Initial status update
    & $script:UpdateStatusDisplay
    
    # Show form
    $form.ShowDialog() | Out-Null
}

# Main execution
if ($Help) {
    Write-Host "FreeRADIUS Google SSO Manager"
    Write-Host "Usage: .\freeradius-manager.ps1"
    Write-Host ""
    Write-Host "This utility provides a GUI for managing the FreeRADIUS Google SSO service."
    exit 0
}

# Check administrator privileges
if (-not (Test-Administrator)) {
    [System.Windows.Forms.MessageBox]::Show("This utility should be run as Administrator for full functionality.", "Administrator Recommended", "OK", "Warning")
}

# Show main window
Show-MainWindow
