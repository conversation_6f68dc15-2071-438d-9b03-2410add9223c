#!/bin/bash

# validate-installer.sh
# Validates that all required files for the one-click installer are present

echo "==================================================================="
echo "FreeRADIUS Google SSO - One-Click Installer Validation"
echo "==================================================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check required installer files
print_status "Checking installer files..."

installer_files=(
    "create-installer.ps1"
    "create-deployment-package.ps1"
    "installer-launcher.bat"
    "freeradius-manager.ps1"
    "ONE_CLICK_INSTALLER_README.md"
)

missing_installer_files=()
for file in "${installer_files[@]}"; do
    if [[ -f "$file" ]]; then
        print_success "Found: $file"
    else
        print_error "Missing: $file"
        missing_installer_files+=("$file")
    fi
done

# Check core project files
print_status "Checking core project files..."

core_files=(
    "src/rlm_google_sso.c"
    "src/rlm_google_sso.h"
    "config/google_sso.conf"
    "CMakeLists.txt"
    "dictionary.google_sso"
    "setup.sh"
    "README.md"
)

missing_core_files=()
for file in "${core_files[@]}"; do
    if [[ -f "$file" ]]; then
        print_success "Found: $file"
    else
        print_error "Missing: $file"
        missing_core_files+=("$file")
    fi
done

# Check deployment files
print_status "Checking deployment files..."

deployment_files=(
    "deploy-windows.ps1"
    "manage-windows-service.ps1"
    "WINDOWS_DEPLOYMENT_GUIDE.md"
    "DEPLOYMENT_GUIDE.md"
)

missing_deployment_files=()
for file in "${deployment_files[@]}"; do
    if [[ -f "$file" ]]; then
        print_success "Found: $file"
    else
        print_warning "Missing: $file"
        missing_deployment_files+=("$file")
    fi
done

# Check documentation
print_status "Checking documentation files..."

doc_files=(
    "docs/google_workspace_setup.md"
    "examples/client_configs.md"
)

missing_doc_files=()
for file in "${doc_files[@]}"; do
    if [[ -f "$file" ]]; then
        print_success "Found: $file"
    else
        print_warning "Missing: $file"
        missing_doc_files+=("$file")
    fi
done

echo
echo "==================================================================="
echo "VALIDATION SUMMARY"
echo "==================================================================="

# Summary
total_files=$((${#installer_files[@]} + ${#core_files[@]} + ${#deployment_files[@]} + ${#doc_files[@]}))
missing_files=$((${#missing_installer_files[@]} + ${#missing_core_files[@]} + ${#missing_deployment_files[@]} + ${#missing_doc_files[@]}))
found_files=$((total_files - missing_files))

echo "Files found: $found_files/$total_files"

if [[ ${#missing_installer_files[@]} -eq 0 && ${#missing_core_files[@]} -eq 0 ]]; then
    print_success "All critical files present - installer is ready!"
    echo
    echo "NEXT STEPS:"
    echo "1. On a Windows machine, run: create-deployment-package.ps1"
    echo "2. Distribute the generated ZIP file"
    echo "3. Users can extract and run installer-launcher.bat as Administrator"
    echo
    echo "INSTALLER FEATURES:"
    echo "✅ GUI-based installation with progress tracking"
    echo "✅ Automatic prerequisite installation (WSL2, Ubuntu, tools)"
    echo "✅ Complete FreeRADIUS deployment and configuration"
    echo "✅ Windows service integration with monitoring"
    echo "✅ Firewall configuration and network setup"
    echo "✅ Desktop shortcuts and management tools"
    echo "✅ Post-installation testing and validation"
    echo
    exit_code=0
else
    print_error "Missing critical files - installer incomplete!"
    echo
    echo "MISSING INSTALLER FILES:"
    for file in "${missing_installer_files[@]}"; do
        echo "  - $file"
    done
    echo
    echo "MISSING CORE FILES:"
    for file in "${missing_core_files[@]}"; do
        echo "  - $file"
    done
    echo
    exit_code=1
fi

if [[ ${#missing_deployment_files[@]} -gt 0 || ${#missing_doc_files[@]} -gt 0 ]]; then
    print_warning "Some optional files are missing but installer will work"
    echo
    echo "MISSING OPTIONAL FILES:"
    for file in "${missing_deployment_files[@]}" "${missing_doc_files[@]}"; do
        echo "  - $file"
    done
    echo
fi

echo "==================================================================="
echo "INSTALLER PACKAGE CONTENTS:"
echo "==================================================================="
echo
echo "📦 MAIN INSTALLER:"
echo "  installer-launcher.bat          - Simple batch launcher (run this)"
echo "  FreeRADIUS-GoogleSSO-Installer.ps1  - Main PowerShell installer with GUI"
echo
echo "🛠️ MANAGEMENT TOOLS:"
echo "  freeradius-manager.ps1          - Post-installation management GUI"
echo "  manage-windows-service.ps1      - Service management utilities"
echo
echo "📋 CREATION SCRIPTS:"
echo "  create-installer.ps1            - Creates installer packages"
echo "  create-deployment-package.ps1   - Creates distribution packages"
echo
echo "📚 DOCUMENTATION:"
echo "  ONE_CLICK_INSTALLER_README.md   - Complete installer guide"
echo "  WINDOWS_DEPLOYMENT_GUIDE.md     - Detailed Windows deployment"
echo "  README.md                       - Project overview"
echo
echo "🔧 SOURCE CODE:"
echo "  src/                            - FreeRADIUS module source"
echo "  config/                         - Configuration templates"
echo "  scripts/                        - Linux installation scripts"
echo
echo "==================================================================="

exit $exit_code
