# create-deployment-package.ps1
# Creates a complete deployment package for distribution

param(
    [string]$OutputPath = "FreeRADIUS-GoogleSSO-Installer.zip",
    [string]$Version = "1.0.0",
    [switch]$Help
)

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Show-Help {
    Write-Host "FreeRADIUS Google SSO Deployment Package Creator"
    Write-Host ""
    Write-Host "USAGE:"
    Write-Host "  .\create-deployment-package.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:"
    Write-Host "  -OutputPath <path>    Output ZIP file path (default: FreeRADIUS-GoogleSSO-Installer.zip)"
    Write-Host "  -Version <version>    Version number (default: 1.0.0)"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "EXAMPLES:"
    Write-Host "  .\create-deployment-package.ps1"
    Write-Host "  .\create-deployment-package.ps1 -OutputPath C:\temp\installer.zip -Version 1.1.0"
}

function Test-RequiredFiles {
    Write-Status "Checking required files..."
    
    $requiredFiles = @(
        "src\rlm_google_sso.c",
        "src\rlm_google_sso.h",
        "config\google_sso.conf",
        "CMakeLists.txt",
        "dictionary.google_sso",
        "setup.sh",
        "README.md"
    )
    
    $missing = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missing += $file
        }
    }
    
    if ($missing.Count -gt 0) {
        Write-Error "Missing required files:"
        foreach ($file in $missing) {
            Write-Host "  - $file" -ForegroundColor Red
        }
        return $false
    }
    
    Write-Success "All required files found"
    return $true
}

function Create-PackageStructure {
    Write-Status "Creating package structure..."
    
    $tempDir = "temp-package"
    
    # Clean up any existing temp directory
    if (Test-Path $tempDir) {
        Remove-Item -Path $tempDir -Recurse -Force
    }
    
    # Create directory structure
    $dirs = @(
        $tempDir,
        "$tempDir\src",
        "$tempDir\config",
        "$tempDir\scripts",
        "$tempDir\docs",
        "$tempDir\examples"
    )
    
    foreach ($dir in $dirs) {
        New-Item -ItemType Directory -Force -Path $dir | Out-Null
    }
    
    return $tempDir
}

function Copy-ProjectFiles {
    param([string]$TempDir)
    
    Write-Status "Copying project files..."
    
    # Copy source files
    Copy-Item -Path "src\*" -Destination "$TempDir\src\" -Recurse -Force
    Copy-Item -Path "config\*" -Destination "$TempDir\config\" -Recurse -Force
    Copy-Item -Path "CMakeLists.txt" -Destination "$TempDir\" -Force
    Copy-Item -Path "dictionary.google_sso" -Destination "$TempDir\" -Force
    
    # Copy scripts
    Copy-Item -Path "setup.sh" -Destination "$TempDir\scripts\" -Force
    if (Test-Path "deploy.sh") {
        Copy-Item -Path "deploy.sh" -Destination "$TempDir\scripts\" -Force
    }
    
    # Copy documentation
    Copy-Item -Path "README.md" -Destination "$TempDir\docs\" -Force
    Copy-Item -Path "DEPLOYMENT_GUIDE.md" -Destination "$TempDir\docs\" -Force -ErrorAction SilentlyContinue
    Copy-Item -Path "WINDOWS_DEPLOYMENT_GUIDE.md" -Destination "$TempDir\docs\" -Force -ErrorAction SilentlyContinue
    
    # Copy examples if they exist
    if (Test-Path "examples") {
        Copy-Item -Path "examples\*" -Destination "$TempDir\examples\" -Recurse -Force
    }
    
    Write-Success "Project files copied"
}

function Create-InstallerFiles {
    param([string]$TempDir)
    
    Write-Status "Creating installer files..."
    
    # Run the installer creator to generate the main installer
    & ".\create-installer.ps1" -OutputDir $TempDir -Version $Version
    
    # Copy the batch launcher
    Copy-Item -Path "installer-launcher.bat" -Destination "$TempDir\" -Force
    
    # Create README for the installer package
    $readmeContent = @"
# FreeRADIUS Google SSO Module - Windows Installer

## Quick Start

1. **Run as Administrator**: Right-click `installer-launcher.bat` and select "Run as administrator"
2. **Follow the GUI**: The installer will guide you through the setup process
3. **Provide Required Information**:
   - Google Workspace domain (e.g., company.com)
   - Admin email address
   - Google service account key file (JSON format)

## What This Installer Does

- Installs WSL2 and Ubuntu (if not already installed)
- Downloads and compiles FreeRADIUS with the Google SSO module
- Configures the module with your Google Workspace settings
- Sets up a Windows service to manage FreeRADIUS
- Configures Windows Firewall rules
- Provides testing and management tools

## System Requirements

- Windows Server 2019/2022 or Windows 10/11 Pro
- Administrator privileges
- Internet connection
- 4GB+ RAM recommended
- 10GB+ free disk space

## Prerequisites

Before running the installer, ensure you have:

1. **Google Workspace Admin Access**
2. **Service Account Key**: 
   - Create a service account in Google Cloud Console
   - Enable domain-wide delegation
   - Download the JSON key file
3. **Network Access**: Ensure the server can reach Google APIs

## Files Included

- `installer-launcher.bat` - Main installer launcher (run this)
- `FreeRADIUS-GoogleSSO-Installer.ps1` - PowerShell installer script
- `src/` - Source code for the FreeRADIUS module
- `config/` - Configuration templates
- `scripts/` - Linux installation scripts
- `docs/` - Documentation and guides
- `examples/` - Example configurations

## Support

For detailed setup instructions, see:
- `docs/WINDOWS_DEPLOYMENT_GUIDE.md`
- `docs/DEPLOYMENT_GUIDE.md`

For troubleshooting:
- Check the installation log at `C:\FreeRADIUS-GoogleSSO\install.log`
- View Windows Event Logs for service issues
- Run `freeradius -X` in WSL2 for debugging

## Version

Version: $Version
Build Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@
    
    $readmeContent | Out-File -FilePath "$TempDir\README.txt" -Encoding UTF8
    
    Write-Success "Installer files created"
}

function Create-ZipPackage {
    param([string]$TempDir, [string]$OutputPath)
    
    Write-Status "Creating ZIP package..."
    
    # Remove existing output file if it exists
    if (Test-Path $OutputPath) {
        Remove-Item -Path $OutputPath -Force
    }
    
    # Create ZIP file
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($TempDir, $OutputPath)
        Write-Success "ZIP package created: $OutputPath"
        return $true
    } catch {
        Write-Error "Failed to create ZIP package: $($_.Exception.Message)"
        return $false
    }
}

function Cleanup-TempFiles {
    param([string]$TempDir)
    
    Write-Status "Cleaning up temporary files..."
    
    if (Test-Path $TempDir) {
        Remove-Item -Path $TempDir -Recurse -Force
    }
    
    Write-Success "Cleanup completed"
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Status "Creating FreeRADIUS Google SSO deployment package..."

# Check required files
if (-not (Test-RequiredFiles)) {
    Write-Error "Cannot create package due to missing files"
    exit 1
}

# Create package
$tempDir = Create-PackageStructure
Copy-ProjectFiles -TempDir $tempDir
Create-InstallerFiles -TempDir $tempDir

if (Create-ZipPackage -TempDir $tempDir -OutputPath $OutputPath) {
    Cleanup-TempFiles -TempDir $tempDir
    
    Write-Success "Deployment package created successfully!"
    Write-Host ""
    Write-Host "Package: $OutputPath"
    Write-Host "Size: $([math]::Round((Get-Item $OutputPath).Length / 1MB, 2)) MB"
    Write-Host ""
    Write-Host "To distribute:"
    Write-Host "1. Send the ZIP file to target Windows servers"
    Write-Host "2. Extract the ZIP file"
    Write-Host "3. Right-click 'installer-launcher.bat' and 'Run as administrator'"
    
} else {
    Cleanup-TempFiles -TempDir $tempDir
    Write-Error "Failed to create deployment package"
    exit 1
}
