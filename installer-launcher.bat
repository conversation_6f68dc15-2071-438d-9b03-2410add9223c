@echo off
REM FreeRADIUS Google SSO One-Click Installer Launcher
REM This batch file launches the PowerShell installer with proper execution policy

title FreeRADIUS Google SSO Installer

echo.
echo ================================================================
echo  FreeRADIUS Google SSO Module - One-Click Installer
echo ================================================================
echo.
echo This installer will set up FreeRADIUS with Google Workspace
echo authentication for WiFi access on Windows Server.
echo.
echo Requirements:
echo - Windows Server 2019/2022 or Windows 10/11 Pro
echo - Administrator privileges
echo - Internet connection
echo - Google Workspace service account key file
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This installer must be run as Administrator.
    echo.
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Checking PowerShell version...
powershell -Command "if ($PSVersionTable.PSVersion.Major -lt 5) { exit 1 } else { exit 0 }"
if %errorLevel% neq 0 (
    echo ERROR: PowerShell 5.0 or later is required.
    echo Please update PowerShell and try again.
    echo.
    pause
    exit /b 1
)

echo.
echo Starting installer...
echo.

REM Launch PowerShell installer with bypass execution policy
powershell -ExecutionPolicy Bypass -File "%~dp0FreeRADIUS-GoogleSSO-Installer.ps1" -GUI

if %errorLevel% equ 0 (
    echo.
    echo Installation completed successfully!
    echo.
    echo The FreeRADIUS Google SSO service should now be running.
    echo Check the Windows Services console to verify.
    echo.
) else (
    echo.
    echo Installation failed or was cancelled.
    echo Check the installation log for details.
    echo.
)

echo Press any key to exit...
pause >nul
