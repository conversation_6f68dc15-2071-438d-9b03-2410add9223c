# FreeRADIUS Google SSO - One-Click Installer for Windows

This repository now includes a comprehensive one-click installer for Windows that automates the entire deployment process with minimal user effort.

## 🚀 Quick Start

### For End Users (IT Administrators)

1. **Download the installer package** (ZIP file)
2. **Extract** the ZIP file to a folder
3. **Right-click** `installer-launcher.bat` and select **"Run as administrator"**
4. **Follow the GUI prompts** to configure your installation
5. **Done!** Your FreeRADIUS Google SSO server is ready

### For Developers/Distributors

1. **Create the installer package**:
   ```powershell
   .\create-deployment-package.ps1
   ```

2. **Distribute** the generated ZIP file to target servers

## 📦 What's Included

### Installer Components

- **`installer-launcher.bat`** - Simple batch file launcher (run this first)
- **`FreeRADIUS-GoogleSSO-Installer.ps1`** - Main PowerShell installer with GUI
- **`freeradius-manager.ps1`** - Post-installation management utility
- **`create-installer.ps1`** - <PERSON><PERSON><PERSON> to create installer packages
- **`create-deployment-package.ps1`** - <PERSON><PERSON><PERSON> to create distribution packages

### What the Installer Does

✅ **Automatic Prerequisites Installation**
- Installs Chocolatey package manager
- Installs WSL2 and Ubuntu (if not present)
- Installs Git, 7-Zip, NSSM
- Handles system restarts as needed

✅ **Complete FreeRADIUS Deployment**
- Downloads and compiles FreeRADIUS with dependencies
- Builds and installs the Google SSO module
- Configures module with your Google Workspace settings
- Sets up proper file permissions and security

✅ **Windows Service Integration**
- Creates a Windows service wrapper for WSL2
- Configures automatic startup
- Provides service monitoring and restart capabilities
- Integrates with Windows Event Log

✅ **Network Configuration**
- Configures Windows Firewall rules (ports 1812, 1813)
- Sets up proper network access for RADIUS traffic

✅ **Management Tools**
- Creates desktop shortcuts for easy management
- Provides GUI management utility
- Includes testing and debugging tools

## 🖥️ System Requirements

- **Operating System**: Windows Server 2019/2022 or Windows 10/11 Pro
- **Privileges**: Administrator access required
- **Memory**: 4GB+ RAM recommended
- **Storage**: 10GB+ free disk space
- **Network**: Internet connection for Google APIs

## 📋 Prerequisites

Before running the installer, ensure you have:

### 1. Google Workspace Setup
- Google Workspace admin access
- Service account created with domain-wide delegation
- Service account JSON key file downloaded

### 2. Network Requirements
- Server can reach Google APIs (googleapis.com)
- RADIUS ports (1812, 1813) accessible from WiFi infrastructure

## 🎯 Installation Process

### Step 1: Prepare Google Workspace

1. **Create Service Account**:
   - Go to Google Cloud Console
   - Create a new service account
   - Enable domain-wide delegation
   - Download JSON key file

2. **Configure API Access**:
   - Enable Admin SDK API
   - Set up OAuth scopes for user management

### Step 2: Run the Installer

1. **Launch as Administrator**:
   ```batch
   # Right-click and "Run as administrator"
   installer-launcher.bat
   ```

2. **Follow GUI Prompts**:
   - Enter Google Workspace domain
   - Provide admin email address
   - Select service account key file
   - Configure RADIUS shared secret
   - Choose installation options

3. **Wait for Completion**:
   - Installer handles all prerequisites
   - May require system restart for WSL2
   - Displays progress and status updates

### Step 3: Post-Installation

1. **Verify Installation**:
   - Check Windows Services for "FreeRADIUS-GoogleSSO"
   - Use the FreeRADIUS Manager desktop shortcut
   - Test authentication with sample user

2. **Configure WiFi Infrastructure**:
   - Point access points to server IP
   - Use ports 1812 (auth) and 1813 (accounting)
   - Configure shared secret

## 🛠️ Management Tools

### FreeRADIUS Manager GUI

Launch from desktop shortcut or:
```powershell
.\freeradius-manager.ps1
```

**Features**:
- Service status monitoring
- Start/stop/restart controls
- Authentication testing
- Quick access to logs and configuration
- Debug mode launcher

### Command Line Management

```powershell
# Service control
Get-Service FreeRADIUS-GoogleSSO
Start-Service FreeRADIUS-GoogleSSO
Stop-Service FreeRADIUS-GoogleSSO

# View logs
Get-EventLog -LogName Application -Source FreeRADIUS-GoogleSSO -Newest 10

# Test authentication (in WSL2)
wsl -d Ubuntu-20.04 radtest <EMAIL> password localhost 0 testing123

# Debug mode
wsl -d Ubuntu-20.04 sudo freeradius -X
```

## 🔧 Troubleshooting

### Common Issues

1. **WSL2 Installation Fails**:
   - Ensure Windows features are enabled
   - Restart may be required
   - Check Windows version compatibility

2. **Service Won't Start**:
   - Check Event Viewer for errors
   - Verify Google service account key
   - Test FreeRADIUS configuration manually

3. **Authentication Fails**:
   - Verify Google Workspace configuration
   - Check user exists and is active
   - Review FreeRADIUS debug output

### Debug Steps

1. **Check Service Status**:
   ```powershell
   Get-Service FreeRADIUS-GoogleSSO
   ```

2. **View Event Logs**:
   ```powershell
   Get-EventLog -LogName Application -Source FreeRADIUS-GoogleSSO
   ```

3. **Test FreeRADIUS Configuration**:
   ```bash
   wsl -d Ubuntu-20.04 sudo freeradius -C
   ```

4. **Run in Debug Mode**:
   ```bash
   wsl -d Ubuntu-20.04 sudo freeradius -X
   ```

## 📁 File Locations

### Windows Locations
- **Installation**: `C:\FreeRADIUS-GoogleSSO\`
- **Service Logs**: `C:\FreeRADIUS-GoogleSSO\service-output.log`
- **Installation Log**: `C:\FreeRADIUS-GoogleSSO\install.log`

### WSL2 Locations
- **FreeRADIUS Config**: `/etc/freeradius/`
- **Module Config**: `/etc/freeradius/mods-enabled/google_sso`
- **Service Account Key**: `/etc/freeradius/certs/google-service-account.json`
- **FreeRADIUS Logs**: `/var/log/freeradius/`

## 🔄 Updates and Maintenance

### Updating the Module

1. **Stop the service**:
   ```powershell
   Stop-Service FreeRADIUS-GoogleSSO
   ```

2. **Update files in WSL2**:
   ```bash
   wsl -d Ubuntu-20.04 sudo systemctl stop freeradius
   # Replace module files
   wsl -d Ubuntu-20.04 sudo systemctl start freeradius
   ```

3. **Restart the service**:
   ```powershell
   Start-Service FreeRADIUS-GoogleSSO
   ```

### Backup and Restore

**Backup**:
```powershell
# Backup configuration
wsl -d Ubuntu-20.04 sudo tar -czf /mnt/c/temp/freeradius-backup.tar.gz /etc/freeradius/
```

**Restore**:
```powershell
# Restore configuration
wsl -d Ubuntu-20.04 sudo tar -xzf /mnt/c/temp/freeradius-backup.tar.gz -C /
```

## 📞 Support

For issues and support:

1. **Check the logs** first (installation log, service logs, event viewer)
2. **Review the documentation** in the `docs/` folder
3. **Test manually** using debug mode
4. **Check Google Workspace** configuration and API access

## 🎉 Success!

Once installed, you should have:
- ✅ FreeRADIUS running as a Windows service
- ✅ Google SSO authentication working
- ✅ Management tools available
- ✅ Desktop shortcuts for easy access
- ✅ Proper logging and monitoring

Your users can now connect to WiFi using their Google Workspace credentials!
